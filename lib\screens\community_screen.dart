import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../providers/theme_provider.dart';
import '../models/post_model.dart';
import '../models/interaction_models.dart';
import '../services/post_service.dart';
import '../services/search_service.dart';

import '../widgets/search_dialog.dart';
import '../utils/logger.dart';
import 'create_post_screen.dart';

class CommunityScreen extends StatefulWidget {
  const CommunityScreen({super.key});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final ScrollController _scrollController = ScrollController();
  final List<PostModel> _posts = [];
  DocumentSnapshot? _lastDocument;
  bool _isLoading = false;
  bool _hasMorePosts = true;
  String _selectedCategory = 'الكل';
  List<PostCategoryModel> _categories = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadCategories();
    _loadPosts();
    _setupScrollListener();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );

    _animationController.forward();
  }

  void _loadCategories() async {
    final categories = await PostService.getCategories();
    setState(() {
      _categories = [
        PostCategoryModel(
          id: 'all',
          name: 'الكل',
          description: 'جميع المنشورات',
          icon: '📋',
          color: '#6B7280',
        ),
        ...categories,
      ];
    });
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        if (!_isLoading && _hasMorePosts) {
          _loadMorePosts();
        }
      }
    });
  }

  void _loadPosts() {
    setState(() {
      _isLoading = true;
      _posts.clear();
      _lastDocument = null;
      _hasMorePosts = true;
    });

    _loadMorePosts();
  }

  void _loadMorePosts() async {
    if (_isLoading || !_hasMorePosts) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // استخدام Future بدلاً من Stream لتجنب التحديث المستمر
      final newPosts = await PostService.getPosts(
        category: _selectedCategory == 'الكل' ? null : _selectedCategory,
        limit: 10,
        lastDocument: _lastDocument,
      );

      if (mounted) {
        setState(() {
          if (_lastDocument == null) {
            _posts.clear();
          }
          _posts.addAll(newPosts);
          _hasMorePosts = newPosts.length == 10;

          // تحديث آخر مستند للصفحة التالية
          if (newPosts.isNotEmpty) {
            // سنحتاج لإضافة هذا في PostService
            // _lastDocument = newPosts.last.documentSnapshot;
          }

          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('خطأ في تحميل المنشورات', 'CommunityScreen', e);
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// عرض نافذة البحث
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => SearchDialog(
            onSearch: (query, filters) {
              _performSearch(query, filters);
            },
          ),
    );
  }

  /// تنفيذ البحث
  void _performSearch(String query, Map<String, dynamic> filters) async {
    // تطبيق البحث على المنشورات
    setState(() {
      _isLoading = true;
    });

    try {
      final searchResults = await SearchService.searchPosts(
        query: query,
        category: filters['category'],
        tags: filters['tags'],
        sortBy: filters['sortBy'],
      );

      if (mounted) {
        setState(() {
          _posts.clear();
          _posts.addAll(searchResults);
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('خطأ في البحث', 'CommunityScreen', e);
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: _buildBody(themeProvider),
                ),
              );
            },
          ),
          floatingActionButton: _buildFloatingActionButton(themeProvider),
        );
      },
    );
  }

  Widget _buildBody(ThemeProvider themeProvider) {
    return SafeArea(
      child: CustomScrollView(
        controller: _scrollController,
        physics: const BouncingScrollPhysics(),
        slivers: [
          _buildHeader(themeProvider),
          _buildCategoryFilter(themeProvider),
          _buildPostsList(themeProvider),
          if (_isLoading) _buildLoadingIndicator(themeProvider),
          const SliverToBoxAdapter(child: SizedBox(height: 100)),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeProvider themeProvider) {
    return SliverToBoxAdapter(
      child: Container(
        height: 160,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF667EEA), Color(0xFF764BA2), Color(0xFF6366F1)],
          ),
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(24),
            bottomRight: Radius.circular(24),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'مجتمع Legal 2025',
                          style: GoogleFonts.cairo(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'شارك أفكارك واطرح أسئلتك',
                          style: GoogleFonts.cairo(
                            fontSize: 14,
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      // زر البحث
                      Container(
                        margin: const EdgeInsets.only(left: 8),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: GestureDetector(
                          onTap: _showSearchDialog,
                          child: const Icon(
                            Icons.search,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      ),
                      // زر الإشعارات
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: const Icon(
                          Icons.people,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildQuickStat('${_posts.length}', 'منشور'),
                  _buildQuickStat(
                    '${_posts.fold(0, (total, post) => total + post.likes)}',
                    'إعجاب',
                  ),
                  _buildQuickStat(
                    '${_posts.fold(0, (total, post) => total + post.comments.length)}',
                    'تعليق',
                  ),
                  _buildQuickStat(
                    '${_posts.fold(0, (total, post) => total + post.shares)}',
                    'مشاركة',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickStat(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: GoogleFonts.cairo(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 12,
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryFilter(ThemeProvider themeProvider) {
    return SliverToBoxAdapter(
      child: Container(
        height: 60,
        margin: const EdgeInsets.symmetric(vertical: 16),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            final category = _categories[index];
            final isSelected = _selectedCategory == category.name;

            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedCategory = category.name;
                });
                HapticFeedback.lightImpact();
                _loadPosts();
              },
              child: Container(
                margin: const EdgeInsets.only(left: 12),
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color:
                      isSelected
                          ? const Color(0xFF10B981)
                          : (themeProvider.isDarkMode
                              ? const Color(0xFF1E293B)
                              : Colors.white),
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color:
                        isSelected
                            ? const Color(0xFF10B981)
                            : (themeProvider.isDarkMode
                                ? Colors.grey[700]!
                                : Colors.grey[200]!),
                  ),
                  boxShadow:
                      isSelected
                          ? [
                            BoxShadow(
                              color: const Color(
                                0xFF10B981,
                              ).withValues(alpha: 0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ]
                          : null,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(category.icon, style: const TextStyle(fontSize: 16)),
                    const SizedBox(width: 8),
                    Text(
                      category.name,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color:
                            isSelected
                                ? Colors.white
                                : (themeProvider.isDarkMode
                                    ? Colors.white
                                    : Colors.black87),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildPostsList(ThemeProvider themeProvider) {
    if (_posts.isEmpty && !_isLoading) {
      return SliverToBoxAdapter(child: _buildEmptyState(themeProvider));
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        final post = _posts[index];
        return _buildFacebookStylePost(post, themeProvider);
      }, childCount: _posts.length),
    );
  }

  Widget _buildEmptyState(ThemeProvider themeProvider) {
    return Container(
      height: 400,
      margin: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
              ),
              borderRadius: BorderRadius.circular(40),
            ),
            child: const Icon(
              Icons.people_outline,
              size: 40,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'مرحباً بك في مجتمع Legal 2025!',
            style: GoogleFonts.cairo(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'شارك أفكارك، اطرح أسئلتك، وتفاعل مع زملائك\nكن أول من يبدأ المحادثة!',
            textAlign: TextAlign.center,
            style: GoogleFonts.cairo(
              fontSize: 14,
              color:
                  themeProvider.isDarkMode
                      ? Colors.grey[400]
                      : Colors.grey[600],
              height: 1.5,
            ),
          ),
          const SizedBox(height: 30),
          ElevatedButton.icon(
            onPressed: _navigateToCreatePost,
            icon: const Icon(Icons.edit_rounded, size: 20),
            label: Text(
              'إنشاء أول منشور',
              style: GoogleFonts.cairo(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF10B981),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              elevation: 0,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFacebookStylePost(PostModel post, ThemeProvider themeProvider) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color:
            themeProvider.isDarkMode ? const Color(0xFF1E293B) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 12,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with author info
          _buildPostHeader(post, themeProvider),

          // Content
          if (post.content.isNotEmpty) _buildPostContent(post, themeProvider),

          // Images
          if (post.imageUrls.isNotEmpty) _buildPostImages(post, themeProvider),

          // Poll
          if (post.pollData != null) _buildPostPoll(post, themeProvider),

          // Interaction stats
          _buildInteractionStats(post, themeProvider),

          // Action buttons
          _buildActionButtons(post, themeProvider),
        ],
      ),
    );
  }

  Widget _buildLoadingIndicator(ThemeProvider themeProvider) {
    return SliverToBoxAdapter(
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Center(
          child: Column(
            children: [
              CircularProgressIndicator(
                valueColor: const AlwaysStoppedAnimation<Color>(
                  Color(0xFF10B981),
                ),
                strokeWidth: 3,
              ),
              const SizedBox(height: 12),
              Text(
                'جاري تحميل المزيد...',
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color:
                      themeProvider.isDarkMode
                          ? Colors.grey[400]
                          : Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton(ThemeProvider themeProvider) {
    return FloatingActionButton.extended(
      onPressed: _navigateToCreatePost,
      backgroundColor: const Color(0xFF10B981),
      foregroundColor: Colors.white,
      elevation: 8,
      icon: const Icon(Icons.add),
      label: Text(
        'منشور جديد',
        style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
      ),
    );
  }

  void _navigateToCreatePost() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CreatePostScreen()),
    );

    if (result == true) {
      // Refresh posts after creating a new one
      _loadPosts();
    }
  }

  // Facebook-style post components
  Widget _buildPostHeader(PostModel post, ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Avatar
          CircleAvatar(
            radius: 20,
            backgroundColor: const Color(0xFF10B981),
            child: Text(
              post.authorName.isNotEmpty
                  ? post.authorName[0].toUpperCase()
                  : 'م',
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
          const SizedBox(width: 12),
          // Author info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      post.authorName,
                      style: GoogleFonts.cairo(
                        fontWeight: FontWeight.w600,
                        fontSize: 15,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.white
                                : Colors.black87,
                      ),
                    ),
                    if (post.isAnonymous) ...[
                      const SizedBox(width: 6),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'مجهول',
                          style: GoogleFonts.cairo(
                            fontSize: 10,
                            color: Colors.orange[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Text(
                      _formatTime(post.createdAt),
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color:
                            themeProvider.isDarkMode
                                ? Colors.grey[400]
                                : Colors.grey[600],
                      ),
                    ),
                    const SizedBox(width: 8),
                    Icon(
                      Icons.public,
                      size: 12,
                      color:
                          themeProvider.isDarkMode
                              ? Colors.grey[400]
                              : Colors.grey[600],
                    ),
                  ],
                ),
              ],
            ),
          ),
          // More options
          IconButton(
            onPressed: () => _showPostOptions(post),
            icon: Icon(
              Icons.more_horiz,
              color:
                  themeProvider.isDarkMode
                      ? Colors.grey[400]
                      : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostContent(PostModel post, ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        post.content,
        style: GoogleFonts.cairo(
          fontSize: 15,
          height: 1.4,
          color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
    );
  }

  Widget _buildPostImages(PostModel post, ThemeProvider themeProvider) {
    if (post.imageUrls.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 12),
      child:
          post.imageUrls.length == 1
              ? _buildSingleImage(post.imageUrls.first)
              : _buildMultipleImages(post.imageUrls),
    );
  }

  Widget _buildSingleImage(String imageUrl) {
    return ClipRRect(
      child: Image.network(
        imageUrl,
        width: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            height: 200,
            color: Colors.grey[300],
            child: const Center(
              child: Icon(Icons.broken_image, size: 50, color: Colors.grey),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMultipleImages(List<String> imageUrls) {
    return SizedBox(
      height: 200,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: imageUrls.length,
        itemBuilder: (context, index) {
          return Container(
            width: 150,
            margin: EdgeInsets.only(
              left: index == 0 ? 16 : 8,
              right: index == imageUrls.length - 1 ? 16 : 0,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                imageUrls[index],
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.grey[300],
                    child: const Center(
                      child: Icon(Icons.broken_image, color: Colors.grey),
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPostPoll(PostModel post, ThemeProvider themeProvider) {
    // Placeholder for poll widget
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: themeProvider.isDarkMode ? Colors.grey[800] : Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        'استطلاع رأي',
        style: GoogleFonts.cairo(
          color: themeProvider.isDarkMode ? Colors.white : Colors.black87,
        ),
      ),
    );
  }

  Widget _buildInteractionStats(PostModel post, ThemeProvider themeProvider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          if (post.likes > 0) ...[
            Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: Color(0xFF1877F2),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.thumb_up, size: 12, color: Colors.white),
            ),
            const SizedBox(width: 6),
            Text(
              '${post.likes}',
              style: GoogleFonts.cairo(
                fontSize: 13,
                color:
                    themeProvider.isDarkMode
                        ? Colors.grey[400]
                        : Colors.grey[600],
              ),
            ),
          ],
          const Spacer(),
          if (post.comments.isNotEmpty) ...[
            Text(
              '${post.comments.length} تعليق',
              style: GoogleFonts.cairo(
                fontSize: 13,
                color:
                    themeProvider.isDarkMode
                        ? Colors.grey[400]
                        : Colors.grey[600],
              ),
            ),
            const SizedBox(width: 16),
          ],
          if (post.shares > 0) ...[
            Text(
              '${post.shares} مشاركة',
              style: GoogleFonts.cairo(
                fontSize: 13,
                color:
                    themeProvider.isDarkMode
                        ? Colors.grey[400]
                        : Colors.grey[600],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons(PostModel post, ThemeProvider themeProvider) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color:
                themeProvider.isDarkMode
                    ? Colors.grey[700]!
                    : Colors.grey[300]!,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildActionButton(
              icon: Icons.thumb_up_outlined,
              label: 'إعجاب',
              onTap: () => _likePost(post),
              themeProvider: themeProvider,
            ),
          ),
          Expanded(
            child: _buildActionButton(
              icon: Icons.comment_outlined,
              label: 'تعليق',
              onTap: () => _commentOnPost(post),
              themeProvider: themeProvider,
            ),
          ),
          Expanded(
            child: _buildActionButton(
              icon: Icons.share_outlined,
              label: 'مشاركة',
              onTap: () => _sharePost(post),
              themeProvider: themeProvider,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required ThemeProvider themeProvider,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 20,
              color:
                  themeProvider.isDarkMode
                      ? Colors.grey[400]
                      : Colors.grey[600],
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: GoogleFonts.cairo(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color:
                    themeProvider.isDarkMode
                        ? Colors.grey[400]
                        : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  String _formatTime(DateTime? dateTime) {
    if (dateTime == null) return 'الآن';

    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} د';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} س';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} ي';
    } else {
      return '${dateTime.day}/${dateTime.month}';
    }
  }

  void _showPostOptions(PostModel post) {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.bookmark_outline),
                  title: Text('حفظ المنشور', style: GoogleFonts.cairo()),
                  onTap: () => Navigator.pop(context),
                ),
                ListTile(
                  leading: const Icon(Icons.report_outlined),
                  title: Text('إبلاغ عن المنشور', style: GoogleFonts.cairo()),
                  onTap: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
    );
  }

  void _likePost(PostModel post) {
    // Implement like functionality
    HapticFeedback.lightImpact();
  }

  void _commentOnPost(PostModel post) {
    // Implement comment functionality
  }

  void _sharePost(PostModel post) {
    // Implement share functionality
    HapticFeedback.lightImpact();
  }
}
